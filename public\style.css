:root {
  --primary: #6366f1;
  --primary-dark: #4f46e5;
  --success: #10b981;
  --warning: #f59e0b;
  --error: #ef4444;
  --gray-50: #f8fafc;
  --gray-100: #f1f5f9;
  --gray-200: #e2e8f0;
  --gray-300: #cbd5e1;
  --gray-400: #94a3b8;
  --gray-500: #64748b;
  --gray-600: #475569;
  --gray-700: #334155;
  --gray-800: #1e293b;
  --gray-900: #0f172a;
  --border-radius: 8px;
  --shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', <PERSON><PERSON>, sans-serif;
  line-height: 1.6;
  color: var(--gray-800);
  background: linear-gradient(135deg, var(--gray-50) 0%, var(--gray-100) 100%);
  min-height: 100vh;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

/* Header */
.header {
  text-align: center;
  margin-bottom: 40px;
  padding: 40px 20px;
  background: white;
  border-radius: var(--border-radius);
  box-shadow: var(--shadow);
}

.header h1 {
  font-size: 2.5rem;
  color: var(--primary);
  margin-bottom: 10px;
}

.header p {
  font-size: 1.1rem;
  color: var(--gray-600);
}

.quick-guide {
  margin-top: 20px;
  padding: 15px;
  background: var(--gray-50);
  border-radius: var(--border-radius);
  border-left: 4px solid var(--primary);
}

.quick-guide p {
  margin-bottom: 5px;
  font-size: 0.95rem;
}

.quick-guide small {
  color: var(--gray-500);
}

.quick-guide a {
  color: var(--primary);
  text-decoration: none;
}

.quick-guide a:hover {
  text-decoration: underline;
}

/* Step Sections */
.step-section {
  background: white;
  border-radius: var(--border-radius);
  box-shadow: var(--shadow);
  margin-bottom: 30px;
  overflow: hidden;
}

.step-header {
  background: var(--primary);
  color: white;
  padding: 20px 30px;
}

.step-header h2 {
  display: flex;
  align-items: center;
  gap: 15px;
  font-size: 1.5rem;
}

.step-number {
  background: rgba(255, 255, 255, 0.2);
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
}

/* Upload Area */
.upload-area {
  border: 3px dashed var(--gray-300);
  border-radius: var(--border-radius);
  padding: 60px 20px;
  text-align: center;
  margin: 30px;
  transition: all 0.3s ease;
  cursor: pointer;
}

.upload-area:hover,
.upload-area.dragover {
  border-color: var(--primary);
  background: var(--gray-50);
}

.upload-icon {
  font-size: 3rem;
  color: var(--gray-400);
  margin-bottom: 20px;
}

.upload-content h3 {
  font-size: 1.5rem;
  margin-bottom: 10px;
  color: var(--gray-700);
}

.btn-link {
  background: none;
  border: none;
  color: var(--primary);
  text-decoration: underline;
  cursor: pointer;
  font-size: inherit;
}

/* URL Input */
.url-input-section {
  padding: 0 30px 30px;
}

.demo-section {
  margin-top: 20px;
  text-align: center;
  padding: 20px;
  background: var(--gray-50);
  border-radius: var(--border-radius);
}

.demo-section p {
  margin-bottom: 10px;
  color: var(--gray-600);
}

.input-group {
  display: flex;
  gap: 10px;
  margin-top: 10px;
}

.input-group input {
  flex: 1;
  padding: 12px;
  border: 2px solid var(--gray-200);
  border-radius: var(--border-radius);
  font-size: 1rem;
}

.input-group input:focus {
  outline: none;
  border-color: var(--primary);
}

/* Image Preview */
.image-preview {
  padding: 30px;
  text-align: center;
}

.image-preview img {
  max-width: 100%;
  max-height: 400px;
  border-radius: var(--border-radius);
  box-shadow: var(--shadow);
  margin-bottom: 20px;
}

.preview-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: var(--gray-50);
  padding: 15px;
  border-radius: var(--border-radius);
}

/* Settings */
.settings-grid {
  padding: 30px;
  display: grid;
  gap: 30px;
}

.setting-group label {
  display: block;
  font-weight: 600;
  margin-bottom: 8px;
  color: var(--gray-700);
}

.setting-group input[type="password"] {
  width: 100%;
  padding: 12px;
  border: 2px solid var(--gray-200);
  border-radius: var(--border-radius);
  font-size: 1rem;
}

.setting-group input[type="password"]:focus {
  outline: none;
  border-color: var(--primary);
}

/* Style Grid */
.style-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 15px;
  margin-top: 10px;
}

.style-option {
  text-align: center;
  cursor: pointer;
  padding: 15px;
  border: 2px solid var(--gray-200);
  border-radius: var(--border-radius);
  transition: all 0.3s ease;
}

.style-option:hover,
.style-option.selected {
  border-color: var(--primary);
  background: var(--gray-50);
}

.style-preview {
  width: 60px;
  height: 60px;
  border-radius: var(--border-radius);
  margin: 0 auto 10px;
}

.style-preview.minimalist {
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
}

.style-preview.luxury {
  background: linear-gradient(135deg, #1f2937 0%, #374151 100%);
}

.style-preview.modern {
  background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
}

.style-preview.natural {
  background: linear-gradient(135deg, #92400e 0%, #a16207 100%);
}

/* Checkbox */
.checkbox-label {
  display: flex !important;
  align-items: center;
  gap: 10px;
  cursor: pointer;
}

.checkbox-label input[type="checkbox"] {
  width: 20px;
  height: 20px;
}

/* Cost Estimator */
.cost-estimator {
  background: var(--gray-50);
  padding: 20px;
  border-radius: var(--border-radius);
  margin: 0 30px 30px;
}

.cost-breakdown {
  margin-top: 15px;
}

.cost-item {
  display: flex;
  justify-content: space-between;
  padding: 8px 0;
  border-bottom: 1px solid var(--gray-200);
}

.cost-total {
  display: flex;
  justify-content: space-between;
  padding: 15px 0 0;
  font-weight: bold;
  font-size: 1.1rem;
  color: var(--primary);
}

/* Buttons */
.btn {
  padding: 12px 24px;
  border: none;
  border-radius: var(--border-radius);
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: inline-flex;
  align-items: center;
  gap: 8px;
  text-decoration: none;
}

.btn-primary {
  background: var(--primary);
  color: white;
}

.btn-primary:hover {
  background: var(--primary-dark);
}

.btn-secondary {
  background: var(--gray-200);
  color: var(--gray-700);
}

.btn-secondary:hover {
  background: var(--gray-300);
}

.btn-danger {
  background: var(--error);
  color: white;
}

.btn-danger:hover {
  background: #dc2626;
}

.btn-large {
  padding: 16px 32px;
  font-size: 1.1rem;
  width: 100%;
  justify-content: center;
  margin: 0 30px 30px;
}

.btn-sm {
  padding: 8px 16px;
  font-size: 0.9rem;
}

/* Progress */
.progress-container {
  padding: 30px;
}

.progress-bar {
  width: 100%;
  height: 20px;
  background: var(--gray-200);
  border-radius: 10px;
  overflow: hidden;
  margin-bottom: 15px;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, var(--primary) 0%, var(--success) 100%);
  width: 0%;
  transition: width 0.5s ease;
}

.progress-info {
  display: flex;
  justify-content: space-between;
  font-weight: 600;
}

/* Processing Steps */
.processing-steps {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
  padding: 0 30px 30px;
}

.process-step {
  text-align: center;
  padding: 20px;
  border: 2px solid var(--gray-200);
  border-radius: var(--border-radius);
  position: relative;
}

.process-step i {
  font-size: 2rem;
  color: var(--gray-400);
  margin-bottom: 10px;
}

.process-step.active {
  border-color: var(--primary);
  background: var(--gray-50);
}

.process-step.active i {
  color: var(--primary);
}

.process-step.completed {
  border-color: var(--success);
  background: #f0fdf4;
}

.process-step.completed i {
  color: var(--success);
}

.step-status {
  position: absolute;
  top: 10px;
  right: 10px;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background: var(--gray-300);
}

.process-step.active .step-status {
  background: var(--primary);
  animation: pulse 2s infinite;
}

.process-step.completed .step-status {
  background: var(--success);
}

.process-step.completed .step-status::after {
  content: '✓';
  color: white;
  font-size: 12px;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

/* Processing Time */
.processing-time {
  text-align: center;
  padding: 0 30px 30px;
  font-size: 1.1rem;
  color: var(--gray-600);
}

/* Results */
.results-summary {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
  padding: 30px;
  background: var(--gray-50);
}

.summary-item {
  display: flex;
  align-items: center;
  gap: 10px;
  font-weight: 600;
}

.summary-item i {
  color: var(--success);
}

/* Results Gallery */
.results-gallery {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
  padding: 30px;
}

.result-item {
  border: 2px solid var(--gray-200);
  border-radius: var(--border-radius);
  overflow: hidden;
  transition: all 0.3s ease;
}

.result-item:hover {
  border-color: var(--primary);
  box-shadow: var(--shadow-lg);
}

.result-item img {
  width: 100%;
  height: 200px;
  object-fit: cover;
}

.result-actions {
  padding: 15px;
  display: flex;
  gap: 10px;
}

.results-actions {
  padding: 0 30px 30px;
  display: flex;
  gap: 15px;
  justify-content: center;
}

/* Modal */
.modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal-content {
  background: white;
  border-radius: var(--border-radius);
  max-width: 500px;
  width: 90%;
  max-height: 90vh;
  overflow-y: auto;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  border-bottom: 1px solid var(--gray-200);
}

.modal-close {
  background: none;
  border: none;
  font-size: 1.5rem;
  cursor: pointer;
  color: var(--gray-500);
}

.modal-body {
  padding: 20px;
}

.modal-footer {
  padding: 20px;
  border-top: 1px solid var(--gray-200);
  text-align: right;
}

/* Loading Overlay */
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(255, 255, 255, 0.9);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  z-index: 999;
}

.spinner {
  width: 50px;
  height: 50px;
  border: 4px solid var(--gray-200);
  border-top: 4px solid var(--primary);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Toast Notifications */
.toast {
  position: fixed;
  top: 20px;
  right: 20px;
  background: white;
  border-radius: var(--border-radius);
  box-shadow: var(--shadow-lg);
  padding: 15px 20px;
  z-index: 1001;
  transform: translateX(400px);
  transition: transform 0.3s ease;
  border-left: 4px solid var(--success);
}

.toast.show {
  transform: translateX(0);
}

.toast.error {
  border-left-color: var(--error);
}

.toast.warning {
  border-left-color: var(--warning);
}

/* Responsive */
@media (max-width: 768px) {
  .container {
    padding: 10px;
  }

  .header h1 {
    font-size: 2rem;
  }

  .upload-area {
    margin: 20px;
    padding: 40px 20px;
  }

  .settings-grid {
    padding: 20px;
  }

  .style-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .processing-steps {
    grid-template-columns: 1fr;
    padding: 0 20px 20px;
  }

  .results-gallery {
    grid-template-columns: 1fr;
    padding: 20px;
  }

  .results-actions {
    flex-direction: column;
  }

  .toast {
    right: 10px;
    left: 10px;
    transform: translateY(-100px);
  }

  .toast.show {
    transform: translateY(0);
  }
}
