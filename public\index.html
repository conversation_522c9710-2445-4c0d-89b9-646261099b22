<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Runware Product Background Generator</title>
    <link rel="stylesheet" href="style.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="container">
        <!-- Header -->
        <header class="header">
            <h1><i class="fas fa-magic"></i> Product Background Generator</h1>
            <p>Tạo background chuyên nghiệp cho sản phẩm với AI</p>
            <div class="quick-guide">
                <p><strong>Hướng dẫn nhanh:</strong>
                1. Upload ảnh sản phẩm →
                2. Nhập API key Runware →
                3. <PERSON><PERSON><PERSON> phong cách →
                4. <PERSON><PERSON><PERSON> đầu xử lý</p>
                <small>Lấy API key miễn ph<PERSON> tại <a href="https://runware.ai" target="_blank">runware.ai</a></small>
            </div>
        </header>

        <!-- Main Content -->
        <main class="main-content">
            <!-- Step 1: Upload -->
            <section class="step-section" id="upload-section">
                <div class="step-header">
                    <h2><span class="step-number">1</span> Upload Ảnh Sản Phẩm</h2>
                </div>

                <div class="upload-area" id="upload-area">
                    <div class="upload-content">
                        <i class="fas fa-cloud-upload-alt upload-icon"></i>
                        <h3>Kéo thả ảnh vào đây</h3>
                        <p>hoặc <button class="btn-link" id="browse-btn">chọn file</button></p>
                        <small>Hỗ trợ: JPG, PNG, WEBP (tối đa 10MB)</small>
                    </div>
                    <input type="file" id="file-input" accept="image/*" hidden>
                </div>

                <div class="url-input-section">
                    <h4>Hoặc nhập URL ảnh:</h4>
                    <div class="input-group">
                        <input type="url" id="image-url" placeholder="https://example.com/image.jpg">
                        <button id="load-url-btn" class="btn btn-secondary">Tải ảnh</button>
                    </div>
                    <div class="demo-section">
                        <p>Hoặc thử với ảnh demo:</p>
                        <button id="demo-btn" class="btn btn-secondary">
                            <i class="fas fa-play"></i> Dùng ảnh demo
                        </button>
                    </div>
                </div>

                <div class="image-preview" id="image-preview" style="display: none;">
                    <img id="preview-img" alt="Preview">
                    <div class="preview-info">
                        <p id="file-info"></p>
                        <button id="remove-image" class="btn btn-danger btn-sm">
                            <i class="fas fa-trash"></i> Xóa
                        </button>
                    </div>
                </div>
            </section>

            <!-- Step 2: Settings -->
            <section class="step-section" id="settings-section" style="display: none;">
                <div class="step-header">
                    <h2><span class="step-number">2</span> Cài Đặt</h2>
                </div>

                <div class="settings-grid">
                    <div class="setting-group">
                        <label for="api-key">Runware API Key:</label>
                        <input type="password" id="api-key" placeholder="Nhập API key của bạn">
                        <small>Lấy API key tại <a href="https://runware.ai" target="_blank">runware.ai</a></small>
                    </div>

                    <div class="setting-group">
                        <label>Phong cách background:</label>
                        <div class="style-grid">
                            <div class="style-option" data-style="minimalist">
                                <div class="style-preview minimalist"></div>
                                <span>Tối giản</span>
                            </div>
                            <div class="style-option" data-style="luxury">
                                <div class="style-preview luxury"></div>
                                <span>Sang trọng</span>
                            </div>
                            <div class="style-option" data-style="modern">
                                <div class="style-preview modern"></div>
                                <span>Hiện đại</span>
                            </div>
                            <div class="style-option" data-style="natural">
                                <div class="style-preview natural"></div>
                                <span>Tự nhiên</span>
                            </div>
                        </div>
                    </div>

                    <div class="setting-group">
                        <label class="checkbox-label">
                            <input type="checkbox" id="enhance-image">
                            <span class="checkmark"></span>
                            Nâng cao chất lượng ảnh (2x upscale)
                        </label>
                    </div>
                </div>

                <div class="cost-estimator">
                    <h4><i class="fas fa-calculator"></i> Ước tính chi phí:</h4>
                    <div class="cost-breakdown">
                        <div class="cost-item">
                            <span>Xóa background:</span>
                            <span>~$0.01</span>
                        </div>
                        <div class="cost-item" id="enhance-cost" style="display: none;">
                            <span>Nâng cao chất lượng:</span>
                            <span>~$0.02</span>
                        </div>
                        <div class="cost-item">
                            <span>Tạo 4 background:</span>
                            <span>~$0.08</span>
                        </div>
                        <div class="cost-total">
                            <span>Tổng cộng:</span>
                            <span id="total-cost">~$0.09</span>
                        </div>
                    </div>
                </div>

                <button id="start-processing" class="btn btn-primary btn-large">
                    <i class="fas fa-play"></i> Bắt Đầu Xử Lý
                </button>
            </section>

            <!-- Step 3: Processing -->
            <section class="step-section" id="processing-section" style="display: none;">
                <div class="step-header">
                    <h2><span class="step-number">3</span> Đang Xử Lý</h2>
                </div>

                <div class="progress-container">
                    <div class="progress-bar">
                        <div class="progress-fill" id="progress-fill"></div>
                    </div>
                    <div class="progress-info">
                        <span id="progress-text">0%</span>
                        <span id="current-step">Chuẩn bị...</span>
                    </div>
                </div>

                <div class="processing-steps">
                    <div class="process-step" id="step-1">
                        <i class="fas fa-cut"></i>
                        <span>Xóa background</span>
                        <div class="step-status"></div>
                    </div>
                    <div class="process-step" id="step-2">
                        <i class="fas fa-magic"></i>
                        <span>Nâng cao chất lượng</span>
                        <div class="step-status"></div>
                    </div>
                    <div class="process-step" id="step-3">
                        <i class="fas fa-palette"></i>
                        <span>Tạo background mới</span>
                        <div class="step-status"></div>
                    </div>
                </div>

                <div class="processing-time">
                    <i class="fas fa-clock"></i>
                    <span>Thời gian: <span id="elapsed-time">00:00</span></span>
                </div>

                <button id="cancel-processing" class="btn btn-danger">
                    <i class="fas fa-stop"></i> Hủy
                </button>
            </section>

            <!-- Step 4: Results -->
            <section class="step-section" id="results-section" style="display: none;">
                <div class="step-header">
                    <h2><span class="step-number">4</span> Kết Quả</h2>
                </div>

                <div class="results-summary">
                    <div class="summary-item">
                        <i class="fas fa-check-circle"></i>
                        <span>Hoàn thành thành công</span>
                    </div>
                    <div class="summary-item">
                        <i class="fas fa-dollar-sign"></i>
                        <span>Chi phí: $<span id="actual-cost">0.00</span></span>
                    </div>
                    <div class="summary-item">
                        <i class="fas fa-images"></i>
                        <span><span id="result-count">0</span> ảnh được tạo</span>
                    </div>
                </div>

                <div class="results-gallery" id="results-gallery">
                    <!-- Results will be populated here -->
                </div>

                <div class="results-actions">
                    <button id="download-all" class="btn btn-primary">
                        <i class="fas fa-download"></i> Tải Tất Cả
                    </button>
                    <button id="start-new" class="btn btn-secondary">
                        <i class="fas fa-plus"></i> Tạo Mới
                    </button>
                </div>
            </section>
        </main>

        <!-- Error Modal -->
        <div class="modal" id="error-modal" style="display: none;">
            <div class="modal-content">
                <div class="modal-header">
                    <h3><i class="fas fa-exclamation-triangle"></i> Lỗi</h3>
                    <button class="modal-close" id="close-error">&times;</button>
                </div>
                <div class="modal-body">
                    <p id="error-message"></p>
                </div>
                <div class="modal-footer">
                    <button class="btn btn-primary" id="error-ok">OK</button>
                </div>
            </div>
        </div>

        <!-- Loading Overlay -->
        <div class="loading-overlay" id="loading-overlay" style="display: none;">
            <div class="spinner"></div>
            <p>Đang tải...</p>
        </div>
    </div>

    <script src="script.js"></script>
</body>
</html>
