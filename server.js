const express = require('express');
const multer = require('multer');
const axios = require('axios');
const cors = require('cors');
const path = require('path');
const fs = require('fs');
const crypto = require('crypto');
require('dotenv').config();

const app = express();
const PORT = process.env.PORT || 3000;

// Middleware
app.use(cors());
app.use(express.json({ limit: '50mb' }));
app.use(express.urlencoded({ extended: true, limit: '50mb' }));
app.use(express.static('public'));

// Create uploads directory if it doesn't exist
const uploadsDir = path.join(__dirname, 'uploads');
if (!fs.existsSync(uploadsDir)) {
  fs.mkdirSync(uploadsDir, { recursive: true });
}

// Multer configuration for file uploads
const storage = multer.diskStorage({
  destination: 'uploads/',
  filename: (req, file, cb) => {
    const uniqueId = Date.now() + '-' + Math.round(Math.random() * 1E9);
    cb(null, uniqueId + path.extname(file.originalname));
  }
});

const upload = multer({
  storage,
  limits: { fileSize: parseInt(process.env.MAX_FILE_SIZE) },
  fileFilter: (req, file, cb) => {
    const allowedTypes = /jpeg|jpg|png|webp/;
    const extname = allowedTypes.test(path.extname(file.originalname).toLowerCase());
    const mimetype = allowedTypes.test(file.mimetype);

    if (mimetype && extname) {
      return cb(null, true);
    } else {
      cb(new Error('Invalid file type. Only JPEG, PNG, and WEBP are allowed.'));
    }
  }
});

// Runware Service Class
class RunwareService {
  constructor(apiKey) {
    this.apiKey = apiKey;
    this.baseUrl = 'https://api.runware.ai/v1';
  }

  async apiCall(payload) {
    try {
      const response = await axios.post(this.baseUrl, payload, {
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${this.apiKey}`
        },
        timeout: 60000
      });
      return response.data;
    } catch (error) {
      console.error('Runware API Error:', error.response?.data || error.message);
      throw new Error(error.response?.data?.message || 'API call failed');
    }
  }

  async removeBackground(imageInput) {
    const payload = [{
      taskType: "imageBackgroundRemoval",
      taskUUID: crypto.randomUUID(),
      inputImage: imageInput,
      model: "runware:112@5", // BiRefNet General
      outputFormat: "PNG",
      outputType: "URL",
      includeCost: true
    }];

    return await this.apiCall(payload);
  }

  async enhanceImage(imageUrl, factor = 2) {
    const payload = [{
      taskType: "imageUpscale",
      taskUUID: crypto.randomUUID(),
      inputImage: imageUrl,
      upscaleFactor: factor,
      outputFormat: "PNG",
      outputType: "URL",
      includeCost: true
    }];

    return await this.apiCall(payload);
  }

  async generateBackgrounds(productUrl, style, count = 4) {
    const prompts = {
      minimalist: "clean white background, minimalist studio, soft shadows, professional product photography",
      luxury: "luxury marble surface, elegant lighting, premium feel, professional product photography",
      modern: "modern gradient background, sleek design, contemporary lighting, professional product photography",
      natural: "natural wood surface, organic lighting, warm tones, professional product photography"
    };

    const payload = [{
      taskType: "imageInference",
      taskUUID: crypto.randomUUID(),
      positivePrompt: prompts[style] || prompts.minimalist,
      negativePrompt: "blurry, low quality, distorted, overexposed, dark, poor lighting",
      initImage: productUrl,
      strength: 0.35,
      model: "runware:100@1", // SDXL
      numberResults: count,
      outputType: "URL",
      includeCost: true,
      controlNet: [{
        model: "runware:101@1",
        guideImage: productUrl,
        weight: 0.8
      }]
    }];

    return await this.apiCall(payload);
  }
}

// Routes
app.get('/', (req, res) => {
  res.sendFile(path.join(__dirname, 'public', 'index.html'));
});

// File upload endpoint
app.post('/api/upload', upload.single('image'), (req, res) => {
  try {
    if (!req.file) {
      return res.status(400).json({ error: 'No file uploaded' });
    }

    const fileUrl = `${req.protocol}://${req.get('host')}/uploads/${req.file.filename}`;

    res.json({
      success: true,
      filename: req.file.filename,
      originalName: req.file.originalname,
      size: req.file.size,
      url: fileUrl
    });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// Helper function to convert image to base64
async function imageToBase64(imageUrl) {
  try {
    if (imageUrl.startsWith('data:')) {
      return imageUrl; // Already base64
    }

    if (imageUrl.startsWith('http://localhost') || imageUrl.startsWith('http://127.0.0.1')) {
      // Local file - read from disk
      const filename = path.basename(imageUrl);
      const filePath = path.join(__dirname, 'uploads', filename);

      if (fs.existsSync(filePath)) {
        const fileBuffer = fs.readFileSync(filePath);
        const mimeType = getMimeType(filePath);
        return `data:${mimeType};base64,${fileBuffer.toString('base64')}`;
      }
    }

    // External URL - fetch and convert
    const response = await axios.get(imageUrl, { responseType: 'arraybuffer' });
    const buffer = Buffer.from(response.data);
    const mimeType = response.headers['content-type'] || 'image/jpeg';
    return `data:${mimeType};base64,${buffer.toString('base64')}`;
  } catch (error) {
    throw new Error('Failed to convert image to base64: ' + error.message);
  }
}

function getMimeType(filePath) {
  const ext = path.extname(filePath).toLowerCase();
  const mimeTypes = {
    '.jpg': 'image/jpeg',
    '.jpeg': 'image/jpeg',
    '.png': 'image/png',
    '.webp': 'image/webp'
  };
  return mimeTypes[ext] || 'image/jpeg';
}

// Process image endpoint
app.post('/api/process', async (req, res) => {
  try {
    const { imageUrl, apiKey, style = 'minimalist', enhance = false } = req.body;

    if (!imageUrl || !apiKey) {
      return res.status(400).json({ error: 'Image URL and API key are required' });
    }

    const runware = new RunwareService(apiKey);
    const results = {};

    // Convert image to base64 for Runware API
    console.log('Converting image to base64...');
    const base64Image = await imageToBase64(imageUrl);

    // Step 1: Remove background
    console.log('Removing background...');
    const bgRemovalResult = await runware.removeBackground(base64Image);
    results.backgroundRemoval = bgRemovalResult;

    if (!bgRemovalResult || !bgRemovalResult[0]?.imageURL) {
      throw new Error('Background removal failed');
    }

    const cutoutUrl = bgRemovalResult[0].imageURL;

    // Step 2: Enhance image (optional)
    let enhancedUrl = cutoutUrl;
    if (enhance) {
      console.log('Enhancing image...');
      const enhanceResult = await runware.enhanceImage(cutoutUrl, 2);
      results.enhancement = enhanceResult;

      if (enhanceResult && enhanceResult[0]?.imageURL) {
        enhancedUrl = enhanceResult[0].imageURL;
      }
    }

    // Step 3: Generate new backgrounds
    console.log('Generating backgrounds...');
    const backgroundsResult = await runware.generateBackgrounds(enhancedUrl, style, 4);
    results.backgrounds = backgroundsResult;

    res.json({
      success: true,
      results: results,
      cutoutUrl: enhancedUrl,
      totalCost: calculateTotalCost(results)
    });

  } catch (error) {
    console.error('Processing error:', error);
    res.status(500).json({ error: error.message });
  }
});

// Test API key endpoint
app.post('/api/test-key', async (req, res) => {
  try {
    const { apiKey } = req.body;

    if (!apiKey) {
      return res.status(400).json({ error: 'API key is required' });
    }

    // Test with a simple request to validate API key
    const response = await axios.post('https://api.runware.ai/v1', [{
      taskType: "imageBackgroundRemoval",
      taskUUID: crypto.randomUUID(),
      inputImage: "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==", // 1x1 transparent pixel
      model: "runware:112@5",
      outputFormat: "PNG",
      outputType: "URL"
    }], {
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${apiKey}`
      },
      timeout: 10000
    });

    res.json({ success: true, message: 'API key is valid' });
  } catch (error) {
    console.error('API key test error:', error.response?.data || error.message);
    if (error.response?.status === 401) {
      res.status(401).json({ error: 'Invalid API key' });
    } else {
      res.status(500).json({ error: 'Failed to validate API key' });
    }
  }
});

// Serve uploaded files
app.use('/uploads', express.static('uploads'));

// Calculate total cost from results
function calculateTotalCost(results) {
  let total = 0;
  Object.values(results).forEach(result => {
    if (Array.isArray(result)) {
      result.forEach(item => {
        if (item.cost) total += item.cost;
      });
    }
  });
  return total;
}

// Cleanup old files (run every hour)
setInterval(() => {
  const uploadsPath = path.join(__dirname, 'uploads');
  fs.readdir(uploadsPath, (err, files) => {
    if (err) return;

    files.forEach(file => {
      const filePath = path.join(uploadsPath, file);
      fs.stat(filePath, (err, stats) => {
        if (err) return;

        // Delete files older than 24 hours
        const now = new Date().getTime();
        const fileTime = new Date(stats.ctime).getTime();
        if (now - fileTime > 24 * 60 * 60 * 1000) {
          fs.unlink(filePath, () => {});
        }
      });
    });
  });
}, 60 * 60 * 1000); // Run every hour

app.listen(PORT, () => {
  console.log(`🚀 Server running on http://localhost:${PORT}`);
  console.log(`📁 Upload directory: ${uploadsDir}`);
});
