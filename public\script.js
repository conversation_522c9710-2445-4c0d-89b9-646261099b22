class ProductBackgroundGenerator {
    constructor() {
        this.currentImage = null;
        this.currentImageUrl = null;
        this.selectedStyle = 'minimalist';
        this.enhanceImage = false;
        this.processingStartTime = null;
        this.processingInterval = null;

        this.initializeElements();
        this.bindEvents();
        this.loadSavedSettings();
    }

    initializeElements() {
        // Upload elements
        this.uploadArea = document.getElementById('upload-area');
        this.fileInput = document.getElementById('file-input');
        this.browseBtn = document.getElementById('browse-btn');
        this.imageUrlInput = document.getElementById('image-url');
        this.loadUrlBtn = document.getElementById('load-url-btn');
        this.demoBtn = document.getElementById('demo-btn');
        this.imagePreview = document.getElementById('image-preview');
        this.previewImg = document.getElementById('preview-img');
        this.fileInfo = document.getElementById('file-info');
        this.removeImageBtn = document.getElementById('remove-image');

        // Settings elements
        this.apiKeyInput = document.getElementById('api-key');
        this.styleOptions = document.querySelectorAll('.style-option');
        this.enhanceCheckbox = document.getElementById('enhance-image');
        this.enhanceCost = document.getElementById('enhance-cost');
        this.totalCost = document.getElementById('total-cost');
        this.startProcessingBtn = document.getElementById('start-processing');

        // Processing elements
        this.progressFill = document.getElementById('progress-fill');
        this.progressText = document.getElementById('progress-text');
        this.currentStep = document.getElementById('current-step');
        this.elapsedTime = document.getElementById('elapsed-time');
        this.cancelProcessingBtn = document.getElementById('cancel-processing');
        this.processSteps = document.querySelectorAll('.process-step');

        // Results elements
        this.actualCost = document.getElementById('actual-cost');
        this.resultCount = document.getElementById('result-count');
        this.resultsGallery = document.getElementById('results-gallery');
        this.downloadAllBtn = document.getElementById('download-all');
        this.startNewBtn = document.getElementById('start-new');

        // Modal elements
        this.errorModal = document.getElementById('error-modal');
        this.errorMessage = document.getElementById('error-message');
        this.closeErrorBtn = document.getElementById('close-error');
        this.errorOkBtn = document.getElementById('error-ok');
        this.loadingOverlay = document.getElementById('loading-overlay');

        // Sections
        this.sections = {
            upload: document.getElementById('upload-section'),
            settings: document.getElementById('settings-section'),
            processing: document.getElementById('processing-section'),
            results: document.getElementById('results-section')
        };
    }

    bindEvents() {
        // Upload events
        this.browseBtn.addEventListener('click', () => this.fileInput.click());
        this.fileInput.addEventListener('change', (e) => this.handleFileSelect(e.target.files[0]));
        this.loadUrlBtn.addEventListener('click', () => this.handleUrlLoad());
        this.demoBtn.addEventListener('click', () => this.loadDemo());
        this.removeImageBtn.addEventListener('click', () => this.removeImage());

        // Drag and drop
        this.uploadArea.addEventListener('dragover', (e) => this.handleDragOver(e));
        this.uploadArea.addEventListener('dragleave', (e) => this.handleDragLeave(e));
        this.uploadArea.addEventListener('drop', (e) => this.handleDrop(e));
        this.uploadArea.addEventListener('click', () => this.fileInput.click());

        // Settings events
        this.styleOptions.forEach(option => {
            option.addEventListener('click', () => this.selectStyle(option.dataset.style));
        });
        this.enhanceCheckbox.addEventListener('change', () => this.updateCostEstimate());
        this.startProcessingBtn.addEventListener('click', () => this.startProcessing());

        // Processing events
        this.cancelProcessingBtn.addEventListener('click', () => this.cancelProcessing());

        // Results events
        this.downloadAllBtn.addEventListener('click', () => this.downloadAll());
        this.startNewBtn.addEventListener('click', () => this.startNew());

        // Modal events
        this.closeErrorBtn.addEventListener('click', () => this.hideError());
        this.errorOkBtn.addEventListener('click', () => this.hideError());

        // Auto-save settings
        this.apiKeyInput.addEventListener('input', () => this.saveSettings());
        this.apiKeyInput.addEventListener('blur', () => this.testApiKey());
    }

    // File handling methods
    handleFileSelect(file) {
        if (!file) return;

        if (!this.validateFile(file)) return;

        this.showLoading();
        this.uploadFile(file);
    }

    handleUrlLoad() {
        const url = this.imageUrlInput.value.trim();
        if (!url) {
            this.showError('Vui lòng nhập URL ảnh');
            return;
        }

        if (!this.isValidUrl(url)) {
            this.showError('URL không hợp lệ');
            return;
        }

        this.showLoading();
        this.loadImageFromUrl(url);
    }

    handleDragOver(e) {
        e.preventDefault();
        this.uploadArea.classList.add('dragover');
    }

    handleDragLeave(e) {
        e.preventDefault();
        this.uploadArea.classList.remove('dragover');
    }

    handleDrop(e) {
        e.preventDefault();
        this.uploadArea.classList.remove('dragover');

        const files = e.dataTransfer.files;
        if (files.length > 0) {
            this.handleFileSelect(files[0]);
        }
    }

    validateFile(file) {
        const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'];
        const maxSize = 10 * 1024 * 1024; // 10MB

        if (!allowedTypes.includes(file.type)) {
            this.showError('Chỉ hỗ trợ file JPG, PNG, WEBP');
            return false;
        }

        if (file.size > maxSize) {
            this.showError('File không được vượt quá 10MB');
            return false;
        }

        return true;
    }

    isValidUrl(url) {
        try {
            new URL(url);
            return /\.(jpg|jpeg|png|webp)$/i.test(url);
        } catch {
            return false;
        }
    }

    async uploadFile(file) {
        try {
            const formData = new FormData();
            formData.append('image', file);

            const response = await fetch('/api/upload', {
                method: 'POST',
                body: formData
            });

            const result = await response.json();

            if (result.success) {
                this.currentImage = file;
                this.currentImageUrl = result.url;
                this.showImagePreview(result.url, result.originalName, result.size);
                this.showSection('settings');
            } else {
                this.showError(result.error || 'Upload thất bại');
            }
        } catch (error) {
            this.showError('Lỗi upload: ' + error.message);
        } finally {
            this.hideLoading();
        }
    }

    async loadImageFromUrl(url) {
        try {
            // Test if image loads
            const img = new Image();
            img.onload = () => {
                this.currentImageUrl = url;
                this.showImagePreview(url, 'Image from URL', 'Unknown size');
                this.showSection('settings');
                this.hideLoading();
            };
            img.onerror = () => {
                this.showError('Không thể tải ảnh từ URL');
                this.hideLoading();
            };
            img.src = url;
        } catch (error) {
            this.showError('Lỗi tải ảnh: ' + error.message);
            this.hideLoading();
        }
    }

    loadDemo() {
        // Use a demo image URL (a product image)
        const demoUrl = 'https://images.unsplash.com/photo-1542291026-7eec264c27ff?w=500&h=500&fit=crop&crop=center';
        this.imageUrlInput.value = demoUrl;
        this.showLoading();
        this.loadImageFromUrl(demoUrl);
    }

    showImagePreview(url, name, size) {
        this.previewImg.src = url;
        this.fileInfo.textContent = `${name} (${this.formatFileSize(size)})`;
        this.imagePreview.style.display = 'block';
    }

    removeImage() {
        this.currentImage = null;
        this.currentImageUrl = null;
        this.imagePreview.style.display = 'none';
        this.imageUrlInput.value = '';
        this.fileInput.value = '';
        this.showSection('upload');
    }

    // Settings methods
    selectStyle(style) {
        this.selectedStyle = style;
        this.styleOptions.forEach(option => {
            option.classList.toggle('selected', option.dataset.style === style);
        });
        this.saveSettings();
    }

    updateCostEstimate() {
        this.enhanceImage = this.enhanceCheckbox.checked;

        let total = 0.09; // Base cost
        if (this.enhanceImage) {
            total += 0.02;
            this.enhanceCost.style.display = 'flex';
        } else {
            this.enhanceCost.style.display = 'none';
        }

        this.totalCost.textContent = `~$${total.toFixed(2)}`;
        this.saveSettings();
    }

    saveSettings() {
        const settings = {
            apiKey: this.apiKeyInput.value,
            style: this.selectedStyle,
            enhance: this.enhanceImage
        };
        localStorage.setItem('runware-settings', JSON.stringify(settings));
    }

    loadSavedSettings() {
        try {
            const saved = localStorage.getItem('runware-settings');
            if (saved) {
                const settings = JSON.parse(saved);
                if (settings.apiKey) this.apiKeyInput.value = settings.apiKey;
                if (settings.style) this.selectStyle(settings.style);
                if (settings.enhance) {
                    this.enhanceCheckbox.checked = settings.enhance;
                    this.updateCostEstimate();
                }
            }
        } catch (error) {
            console.error('Error loading settings:', error);
        }
    }

    // Processing methods
    async startProcessing() {
        if (!this.currentImageUrl) {
            this.showError('Vui lòng upload ảnh trước');
            return;
        }

        const apiKey = this.apiKeyInput.value.trim();
        if (!apiKey) {
            this.showError('Vui lòng nhập Runware API key');
            return;
        }

        this.showSection('processing');
        this.processingStartTime = Date.now();
        this.startTimer();
        this.updateProgress(0, 'Bắt đầu xử lý...');

        try {
            // Simulate progress updates
            this.updateProgress(10, 'Chuẩn bị ảnh...');
            await this.delay(500);

            this.updateProgress(20, 'Gửi yêu cầu đến Runware API...');
            await this.delay(500);

            const response = await fetch('/api/process', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    imageUrl: this.currentImageUrl,
                    apiKey: apiKey,
                    style: this.selectedStyle,
                    enhance: this.enhanceImage
                })
            });

            this.updateProgress(30, 'Đang xóa background...');

            const result = await response.json();

            if (result.success) {
                this.updateProgress(90, 'Hoàn tất xử lý...');
                await this.delay(500);
                this.showResults(result);
            } else {
                this.showError(result.error || 'Xử lý thất bại');
                this.showSection('settings');
            }
        } catch (error) {
            this.showError('Lỗi xử lý: ' + error.message);
            this.showSection('settings');
        } finally {
            this.stopTimer();
        }
    }

    delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    async testApiKey() {
        const apiKey = this.apiKeyInput.value.trim();
        if (!apiKey || apiKey.length < 10) return;

        try {
            const response = await fetch('/api/test-key', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ apiKey })
            });

            const result = await response.json();

            if (result.success) {
                this.apiKeyInput.style.borderColor = 'var(--success)';
                this.showToast('API key hợp lệ!', 'success');
            } else {
                this.apiKeyInput.style.borderColor = 'var(--error)';
                this.showToast('API key không hợp lệ', 'error');
            }
        } catch (error) {
            this.apiKeyInput.style.borderColor = 'var(--error)';
            console.error('API key test failed:', error);
        }
    }

    updateProgress(percentage, stepText) {
        this.progressFill.style.width = `${percentage}%`;
        this.progressText.textContent = `${Math.round(percentage)}%`;
        this.currentStep.textContent = stepText;

        // Update step indicators
        if (percentage < 30) {
            this.setStepStatus(0, 'active');
        } else if (percentage < 50) {
            this.setStepStatus(0, 'completed');
            this.setStepStatus(1, 'active');
        } else if (percentage < 100) {
            this.setStepStatus(0, 'completed');
            this.setStepStatus(1, 'completed');
            this.setStepStatus(2, 'active');
        } else {
            this.processSteps.forEach(step => this.setStepStatus(step, 'completed'));
        }
    }

    setStepStatus(index, status) {
        const step = this.processSteps[index];
        if (step) {
            step.className = 'process-step ' + status;
        }
    }

    startTimer() {
        this.processingInterval = setInterval(() => {
            const elapsed = Date.now() - this.processingStartTime;
            const minutes = Math.floor(elapsed / 60000);
            const seconds = Math.floor((elapsed % 60000) / 1000);
            this.elapsedTime.textContent = `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
        }, 1000);
    }

    stopTimer() {
        if (this.processingInterval) {
            clearInterval(this.processingInterval);
            this.processingInterval = null;
        }
    }

    cancelProcessing() {
        this.stopTimer();
        this.showSection('settings');
        this.showToast('Đã hủy xử lý', 'warning');
    }

    // Results methods
    showResults(result) {
        this.showSection('results');
        this.actualCost.textContent = result.totalCost.toFixed(4);

        const images = [];
        if (result.results.backgrounds && result.results.backgrounds.length > 0) {
            images.push(...result.results.backgrounds);
        }

        this.resultCount.textContent = images.length;
        this.renderGallery(images, result.cutoutUrl);
        this.updateProgress(100, 'Hoàn thành!');
        this.showToast('Xử lý hoàn thành thành công!', 'success');
    }

    renderGallery(images, cutoutUrl) {
        this.resultsGallery.innerHTML = '';

        // Add cutout image first
        if (cutoutUrl) {
            const cutoutItem = this.createResultItem(cutoutUrl, 'Ảnh đã xóa background', true);
            this.resultsGallery.appendChild(cutoutItem);
        }

        // Add generated backgrounds
        images.forEach((image, index) => {
            if (image.imageURL) {
                const item = this.createResultItem(image.imageURL, `Background ${index + 1}`);
                this.resultsGallery.appendChild(item);
            }
        });
    }

    createResultItem(imageUrl, title, isCutout = false) {
        const item = document.createElement('div');
        item.className = 'result-item';

        item.innerHTML = `
            <img src="${imageUrl}" alt="${title}" loading="lazy">
            <div class="result-actions">
                <button class="btn btn-primary btn-sm download-btn" data-url="${imageUrl}" data-title="${title}">
                    <i class="fas fa-download"></i> Tải về
                </button>
                <button class="btn btn-secondary btn-sm view-btn" data-url="${imageUrl}">
                    <i class="fas fa-eye"></i> Xem
                </button>
            </div>
        `;

        // Bind events
        const downloadBtn = item.querySelector('.download-btn');
        const viewBtn = item.querySelector('.view-btn');

        downloadBtn.addEventListener('click', () => this.downloadImage(imageUrl, title));
        viewBtn.addEventListener('click', () => this.viewImage(imageUrl));

        return item;
    }

    async downloadImage(url, filename) {
        try {
            const response = await fetch(url);
            const blob = await response.blob();
            const downloadUrl = window.URL.createObjectURL(blob);

            const a = document.createElement('a');
            a.href = downloadUrl;
            a.download = `${filename}.png`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);

            window.URL.revokeObjectURL(downloadUrl);
            this.showToast('Đã tải xuống thành công!', 'success');
        } catch (error) {
            this.showError('Lỗi tải xuống: ' + error.message);
        }
    }

    viewImage(url) {
        window.open(url, '_blank');
    }

    async downloadAll() {
        const downloadBtns = this.resultsGallery.querySelectorAll('.download-btn');
        for (let btn of downloadBtns) {
            await this.downloadImage(btn.dataset.url, btn.dataset.title);
            await new Promise(resolve => setTimeout(resolve, 500)); // Delay between downloads
        }
    }

    startNew() {
        this.removeImage();
        this.resetProcessing();
        this.showSection('upload');
    }

    resetProcessing() {
        this.updateProgress(0, 'Chuẩn bị...');
        this.processSteps.forEach(step => {
            step.className = 'process-step';
        });
        this.stopTimer();
        this.elapsedTime.textContent = '00:00';
    }

    // UI helper methods
    showSection(sectionName) {
        Object.values(this.sections).forEach(section => {
            section.style.display = 'none';
        });
        this.sections[sectionName].style.display = 'block';
    }

    showError(message) {
        this.errorMessage.textContent = message;
        this.errorModal.style.display = 'flex';
    }

    hideError() {
        this.errorModal.style.display = 'none';
    }

    showLoading() {
        this.loadingOverlay.style.display = 'flex';
    }

    hideLoading() {
        this.loadingOverlay.style.display = 'none';
    }

    showToast(message, type = 'success') {
        const toast = document.createElement('div');
        toast.className = `toast ${type}`;
        toast.innerHTML = `
            <div style="display: flex; align-items: center; gap: 10px;">
                <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'error' ? 'exclamation-circle' : 'exclamation-triangle'}"></i>
                <span>${message}</span>
            </div>
        `;

        document.body.appendChild(toast);

        setTimeout(() => toast.classList.add('show'), 100);
        setTimeout(() => {
            toast.classList.remove('show');
            setTimeout(() => document.body.removeChild(toast), 300);
        }, 3000);
    }

    formatFileSize(bytes) {
        if (typeof bytes === 'string') return bytes;
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }
}

// Initialize the application
document.addEventListener('DOMContentLoaded', () => {
    new ProductBackgroundGenerator();
});
