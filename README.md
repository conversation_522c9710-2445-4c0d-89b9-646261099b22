# 🧠 Runware Product Background Generator

Website chuyên nghiệp để tạo background cho sản phẩm sử dụng AI của Runware.

## ✨ Tính năng chính

- **Upload ảnh dễ dàng**: Drag & drop hoặc chọn file, hỗ trợ URL
- **Xóa background tự động**: Sử dụng AI BiRefNet General model
- **Tạo background đa dạng**: 4 phong cách khác nhau (<PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON> đ<PERSON>, <PERSON><PERSON> nhiên)
- **Nâng cao chất lượng**: Upscale 2x với AI
- **Giao diện thân thiện**: Responsive design, hỗ trợ mobile
- **Theo dõi tiến trình**: Real-time progress tracking
- **Tải xuống hàng loạt**: Download tất cả kết quả cùng lúc

## 🚀 Cách sử dụng

### 1. Cài đặt và chạy

```bash
# Clone repository
git clone <repository-url>
cd product-background

# Cài đặt dependencies
npm install

# Chạy server
npm start
```

Website sẽ chạy tại: http://localhost:3000

### 2. Sử dụng website

1. **Upload ảnh sản phẩm**:
   - Kéo thả file vào vùng upload
   - Hoặc click "chọn file" để browse
   - Hoặc nhập URL ảnh trực tiếp

2. **Cài đặt**:
   - Nhập Runware API key (lấy tại [runware.ai](https://runware.ai))
   - Chọn phong cách background
   - Tùy chọn nâng cao chất lượng ảnh

3. **Xử lý**:
   - Click "Bắt Đầu Xử Lý"
   - Theo dõi tiến trình real-time
   - Chờ kết quả (thường 1-2 phút)

4. **Kết quả**:
   - Xem gallery các ảnh đã tạo
   - Tải xuống từng ảnh hoặc tất cả
   - Bắt đầu tạo mới

## 🔧 Cấu hình

### Environment Variables (.env)

```env
RUNWARE_API_KEY=your_api_key_here
PORT=3000
MAX_FILE_SIZE=10485760
NODE_ENV=development
```

### Runware API Key

1. Đăng ký tài khoản tại [runware.ai](https://runware.ai)
2. Lấy API key từ dashboard
3. Nhập vào website hoặc cấu hình trong .env

## 📋 Yêu cầu hệ thống

- **Node.js**: >= 16.0.0
- **NPM**: >= 8.0.0
- **RAM**: >= 512MB
- **Disk**: >= 1GB (cho temporary files)

## 🎯 Các model AI được sử dụng

### Background Removal
- **BiRefNet General** (`runware:112@5`): Model chính, chất lượng cao
- **RemBG 1.4** (`runware:109@1`): Backup, tốc độ nhanh

### Image Generation
- **SDXL** (`runware:100@1`): Model chính cho tạo background
- **ControlNet Depth** (`runware:101@1`): Giữ nguyên cấu trúc sản phẩm

### Image Enhancement
- **Upscale 2x**: Nâng cao độ phân giải gấp đôi

## 💰 Chi phí ước tính

- **Xóa background**: ~$0.01
- **Nâng cao chất lượng**: ~$0.02
- **Tạo 4 background**: ~$0.08
- **Tổng cộng**: ~$0.09-0.11 per image

## 🔒 Bảo mật

- API key được lưu local storage (chỉ trên máy bạn)
- Files upload tự động xóa sau 24h
- Không lưu trữ dữ liệu người dùng
- HTTPS ready (production)

## 🐛 Troubleshooting

### Lỗi thường gặp

1. **"Invalid API key"**
   - Kiểm tra API key đã nhập đúng
   - Đảm bảo tài khoản Runware còn credits

2. **"File too large"**
   - File phải < 10MB
   - Nén ảnh trước khi upload

3. **"Processing failed"**
   - Kiểm tra kết nối internet
   - Thử lại sau vài phút
   - Kiểm tra credits Runware

4. **"Upload failed"**
   - Kiểm tra định dạng file (JPG, PNG, WEBP)
   - Thử upload file khác

### Performance Tips

- Sử dụng ảnh có độ phân giải vừa phải (1000-2000px)
- Đảm bảo ảnh sản phẩm rõ nét, không bị mờ
- Chọn background phù hợp với sản phẩm
- Sử dụng enhance chỉ khi cần thiết

## 📞 Hỗ trợ

- **Documentation**: Xem file `scratchpad.md` để biết chi tiết kỹ thuật
- **Runware API**: [runware.ai/docs](https://runware.ai/docs)
- **Issues**: Tạo issue trên GitHub repository

## 📄 License

MIT License - Xem file LICENSE để biết chi tiết.

---

**Được phát triển bởi TiTaiCode** 🚀
