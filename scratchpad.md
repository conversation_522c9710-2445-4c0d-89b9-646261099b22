# 🧠 Runware Product Photography - Complete Development Scratchpad

## 📋 Table of Contents
1. [Architecture Overview](#architecture)
2. [Implementation Process](#implementation)
3. [Runware API Integration](#api-integration)
4. [Model Selection & Optimization](#model-optimization)
5. [UI/UX Requirements](#ui-ux)
6. [Error Handling](#error-handling)
7. [Performance Tips](#performance)
8. [Testing Criteria](#testing)
9. [Code Examples](#code-examples)
10. [Deployment Checklist](#deployment)

---

## 🏗️ Architecture Overview {#architecture}

### **System Components**
```
Frontend (React/Vanilla JS) ←→ Backend (Node.js/Express) ←→ Runware API
       ↓                           ↓                         ↓
   User Interface              File Management          AI Processing
   Progress Tracking          Job Queue System         Image Generation
   Results Gallery            Status Management        Cost Tracking
```

### **Data Flow**
```
1. User uploads image/URL → 2. File validation → 3. API key validation
4. Job creation → 5. Runware processing → 6. Progress polling
7. Results retrieval → 8. Gallery display → 9. Download options
```

### **Core Technologies**
- **Frontend**: HTML5, CSS3, Vanilla JavaScript (no frameworks needed)
- **Backend**: Node.js + Express.js
- **File Upload**: Multer middleware
- **HTTP Client**: Axios for API calls
- **Storage**: Local file system + temporary URLs
- **UI**: Modern responsive design with CSS Grid/Flexbox

---

## 🔄 Implementation Process {#implementation}

### **Phase 1: Basic Setup**
1. **Project Structure**
   ```
   project/
   ├── frontend/ (HTML, CSS, JS)
   ├── backend/ (Express server)
   ├── uploads/ (temporary storage)
   └── config/ (settings)
   ```

2. **Dependencies Installation**
   ```json
   {
     "express": "^4.18.2",
     "multer": "^1.4.5",
     "axios": "^1.6.0",
     "cors": "^2.8.5",
     "dotenv": "^16.3.1"
   }
   ```

3. **Environment Setup**
   ```env
   RUNWARE_API_KEY=yRQ2DLBEKBBPemhULO4sLXLUy4VwITMJ
   PORT=3000
   MAX_FILE_SIZE=10485760
   ```

### **Phase 2: Core Features**
1. **File Upload System**
   - Drag & drop interface
   - URL input option
   - File validation (type, size)
   - Progress indicators

2. **Runware Integration**
   - Background removal
   - Image enhancement
   - Background generation
   - Cost tracking

3. **User Interface**
   - Step-by-step wizard
   - Real-time progress
   - Results gallery
   - Download functionality

### **Phase 3: Advanced Features**
1. **Batch Processing**
2. **Settings Management**
3. **Error Recovery**
4. **Performance Optimization**

---

## 🔌 Runware API Integration {#api-integration}

### **Authentication**
```javascript
const headers = {
  'Content-Type': 'application/json',
  'Authorization': `Bearer ${API_KEY}`
}
```

### **API Endpoints & Usage**
1. **Background Removal**
   ```javascript
   POST https://api.runware.ai/v1
   {
     "taskType": "imageBackgroundRemoval",
     "inputImage": "base64_or_url",
     "model": "runware:112@5", // BiRefNet General
     "outputFormat": "PNG"
   }
   ```

2. **Image Upscaling**
   ```javascript
   {
     "taskType": "imageUpscale", 
     "inputImage": "processed_image_url",
     "upscaleFactor": 2, // 1x-4x
     "outputFormat": "PNG"
   }
   ```

3. **Background Generation**
   ```javascript
   {
     "taskType": "imageInference",
     "positivePrompt": "professional product photography...",
     "initImage": "cutout_image",
     "strength": 0.35,
     "model": "runware:100@1", // SDXL
     "controlNet": [{"model": "runware:101@1"}]
   }
   ```

### **Request Format Pattern**
```javascript
const payload = [{
  "taskType": "...",
  "taskUUID": crypto.randomUUID(),
  "inputImage": "...",
  "outputType": "URL",
  "includeCost": true
}];
```

---

## 🎯 Model Selection & Optimization {#model-optimization}

### **Background Removal Models**
| Model ID | Name | Use Case | Quality | Speed |
|----------|------|----------|---------|-------|
| `runware:109@1` | RemBG 1.4 | General purpose | Good | Fast |
| `runware:110@1` | Bria RMBG 2.0 | High accuracy | Better | Medium |
| `runware:112@5` | BiRefNet General | **Recommended** | Best | Medium |
| `runware:112@9` | BiRefNet Matting | Fine details | Excellent | Slow |

### **Image Generation Models**
- **Primary**: `runware:100@1` (SDXL) - Best quality/speed balance
- **Alternative**: `runware:101@1` (SD 1.5) - Faster, lower cost

### **ControlNet Models**
- **Depth**: `runware:101@1` - Maintain product structure
- **Canny**: `runware:102@1` - Edge preservation
- **Pose**: `runware:103@1` - Human products

### **LoRA Models**
- **Product Photography**: `runware:120@2` - Professional look
- **E-commerce**: `runware:121@1` - Commercial style

### **Optimization Tips**
1. **Quality vs Speed**
   - Production: BiRefNet General + SDXL
   - Fast preview: RemBG + SD 1.5
   - High-end: BiRefNet Matting + SDXL + Final polish

2. **Cost Optimization**
   - Use 2x upscaling (best value)
   - Generate 4 variants (optimal variety)
   - Enable final polish only for premium

3. **Prompt Engineering**
   ```
   Base: "professional product photography, studio lighting, high resolution"
   + Style: "minimalist white background, soft shadows"
   + Quality: "commercial photography, clean composition, 8k, detailed"
   - Negative: "blurry, low quality, distorted, overexposed"
   ```

---

## 🎨 UI/UX Requirements {#ui-ux}

### **Design Principles**
1. **Progressive Disclosure** - Show features step by step
2. **Immediate Feedback** - Real-time progress and status
3. **Error Prevention** - Validate inputs before processing
4. **Accessibility** - Screen reader support, keyboard navigation

### **User Journey**
```
Upload → Configure → Process → Review → Download
  ↓         ↓          ↓        ↓        ↓
Preview   Settings   Progress  Gallery  Batch
```

### **Required Components**
1. **Upload Zone**
   - Drag & drop area (min 300x200px)
   - File browser button
   - URL input field
   - Progress indicator
   - File preview (max 500x500px)

2. **Settings Panel**
   - API key input (password type)
   - Background style selector (grid layout)
   - Advanced options (collapsible)
   - Cost estimator

3. **Progress Tracker**
   - Overall progress bar
   - Step-by-step indicators
   - Elapsed time counter
   - Cancel button

4. **Results Gallery**
   - Grid layout (responsive)
   - Image previews (200x200px)
   - Download buttons
   - Full-size modal view

### **Responsive Breakpoints**
- Mobile: 320px - 768px
- Tablet: 768px - 1024px  
- Desktop: 1024px+

### **Color Scheme**
```css
:root {
  --primary: #6366f1; /* Indigo */
  --success: #10b981; /* Emerald */
  --warning: #f59e0b; /* Amber */
  --error: #ef4444;   /* Red */
  --gray-50: #f8fafc;
  --gray-900: #0f172a;
}
```

---

## ⚠️ Error Handling {#error-handling}

### **Error Categories**
1. **User Input Errors**
   - Invalid file type/size
   - Missing API key
   - Invalid URL format

2. **API Errors**
   - Authentication failure
   - Insufficient credits
   - Rate limiting
   - Processing timeout

3. **System Errors**
   - Network connectivity
   - File upload failure
   - Storage issues

### **Error Handling Strategy**
```javascript
// Retry logic with exponential backoff
async function apiCallWithRetry(payload, maxRetries = 3) {
  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      return await makeApiCall(payload);
    } catch (error) {
      if (attempt === maxRetries) throw error;
      await delay(Math.pow(2, attempt) * 1000);
    }
  }
}

// User-friendly error messages
const ERROR_MESSAGES = {
  'INVALID_API_KEY': 'Please check your Runware API key',
  'FILE_TOO_LARGE': 'File size must be under 10MB',
  'UNSUPPORTED_FORMAT': 'Please upload JPG, PNG, or WEBP files',
  'INSUFFICIENT_CREDITS': 'Please add credits to your Runware account'
};
```

### **Error UI Components**
- Toast notifications for minor errors
- Modal dialogs for critical errors
- Inline validation messages
- Retry buttons with countdown

---

## ⚡ Performance Tips {#performance}

### **Frontend Optimization**
1. **Image Loading**
   - Lazy loading for gallery
   - Progressive image enhancement
   - WebP format support
   - Image compression before upload

2. **JavaScript**
   - Debounce user inputs
   - Use Web Workers for heavy tasks
   - Minimize DOM manipulation
   - Cache API responses

3. **CSS**
   - CSS Grid for layouts
   - Use transforms for animations
   - Minimize repaints/reflows
   - Critical CSS inline

### **Backend Optimization**
1. **File Handling**
   - Stream file uploads
   - Automatic cleanup (24h)
   - Compress temporary files
   - Validate files early

2. **API Calls**
   - Connection pooling
   - Request batching
   - Intelligent retry logic
   - Response caching

3. **Memory Management**
   - Limit concurrent jobs
   - Clean up job data
   - Monitor memory usage
   - Garbage collection tuning

### **Runware API Optimization**
1. **Model Selection**
   - Use fastest models for previews
   - High-quality models for finals
   - Cache model responses

2. **Request Optimization**
   - Batch multiple tasks
   - Optimize image sizes
   - Use appropriate formats

---

## ✅ Testing Criteria {#testing}

### **Functional Testing**
1. **Upload Flow**
   - [ ] Drag & drop works
   - [ ] File validation correct
   - [ ] URL loading works
   - [ ] Preview displays properly

2. **Processing Flow**
   - [ ] API key validation
   - [ ] Settings save/load
   - [ ] Progress tracking accurate
   - [ ] All steps complete

3. **Results Flow**
   - [ ] Gallery renders correctly
   - [ ] Downloads work
   - [ ] Full-size modal works
   - [ ] Batch download functions

### **Error Testing**
1. **Invalid Inputs**
   - [ ] Wrong file types rejected
   - [ ] Large files rejected
   - [ ] Invalid URLs handled
   - [ ] Missing API key caught

2. **API Failures**
   - [ ] Network errors handled
   - [ ] Rate limiting respected
   - [ ] Timeout recovery works
   - [ ] Cost limits enforced

### **Performance Testing**
1. **Load Testing**
   - [ ] 10 concurrent users
   - [ ] Large file uploads (9MB)
   - [ ] Batch processing (5 images)
   - [ ] Memory usage stable

2. **Browser Testing**
   - [ ] Chrome (latest)
   - [ ] Firefox (latest)
   - [ ] Safari (latest)
   - [ ] Edge (latest)

---

## 💻 Code Examples {#code-examples}

### **File Upload Handler**
```javascript
const multer = require('multer');
const path = require('path');

const storage = multer.diskStorage({
  destination: 'uploads/',
  filename: (req, file, cb) => {
    const uniqueId = Date.now() + '-' + Math.round(Math.random() * 1E9);
    cb(null, uniqueId + path.extname(file.originalname));
  }
});

const upload = multer({
  storage,
  limits: { fileSize: 10 * 1024 * 1024 }, // 10MB
  fileFilter: (req, file, cb) => {
    const allowedTypes = /jpeg|jpg|png|webp/;
    const extname = allowedTypes.test(path.extname(file.originalname).toLowerCase());
    const mimetype = allowedTypes.test(file.mimetype);
    
    if (mimetype && extname) {
      return cb(null, true);
    } else {
      cb(new Error('Invalid file type'));
    }
  }
});
```

### **Runware Service Class**
```javascript
class RunwareService {
  constructor(apiKey) {
    this.apiKey = apiKey;
    this.baseUrl = 'https://api.runware.ai/v1';
  }

  async removeBackground(imageInput) {
    const payload = [{
      taskType: "imageBackgroundRemoval",
      taskUUID: crypto.randomUUID(),
      inputImage: imageInput,
      model: "runware:112@5",
      outputFormat: "PNG",
      settings: {
        alphaMatting: true,
        alphaMattingForegroundThreshold: 240
      }
    }];
    
    return await this.apiCall(payload);
  }

  async enhanceImage(imageUrl, factor = 2) {
    const payload = [{
      taskType: "imageUpscale",
      taskUUID: crypto.randomUUID(),
      inputImage: imageUrl,
      upscaleFactor: factor,
      outputFormat: "PNG"
    }];
    
    return await this.apiCall(payload);
  }

  async generateBackgrounds(productUrl, style, count = 4) {
    const prompts = {
      minimalist: "clean white background, minimalist studio, soft shadows",
      luxury: "luxury marble surface, elegant lighting, premium feel"
    };

    const payload = [{
      taskType: "imageInference",
      taskUUID: crypto.randomUUID(),
      positivePrompt: `${prompts[style]}, professional product photography`,
      initImage: productUrl,
      strength: 0.35,
      model: "runware:100@1",
      numberResults: count,
      controlNet: [{
        model: "runware:101@1",
        guideImage: productUrl,
        weight: 0.8
      }]
    }];
    
    return await this.apiCall(payload);
  }
}
```

### **Progress Tracking System**
```javascript
class ProgressTracker {
  constructor(jobId) {
    this.jobId = jobId;
    this.steps = [
      { name: 'Background Removal', weight: 30 },
      { name: 'Image Enhancement', weight: 20 },
      { name: 'Background Generation', weight: 50 }
    ];
    this.currentStep = 0;
    this.progress = 0;
  }

  updateProgress(stepIndex, stepProgress) {
    let totalProgress = 0;
    
    // Calculate completed steps
    for (let i = 0; i < stepIndex; i++) {
      totalProgress += this.steps[i].weight;
    }
    
    // Add current step progress
    if (stepIndex < this.steps.length) {
      totalProgress += (this.steps[stepIndex].weight * stepProgress / 100);
    }
    
    this.progress = Math.min(totalProgress, 100);
    this.currentStep = stepIndex;
    
    // Update UI
    this.updateUI();
  }

  updateUI() {
    document.getElementById('progress-bar').style.width = `${this.progress}%`;
    document.getElementById('progress-text').textContent = `${Math.round(this.progress)}%`;
    document.getElementById('current-step').textContent = this.steps[this.currentStep]?.name || 'Complete';
  }
}
```

---

## 🚀 Deployment Checklist {#deployment}

### **Pre-deployment**
- [ ] Environment variables configured
- [ ] API keys secured
- [ ] File permissions set
- [ ] Upload directories created
- [ ] Error logging enabled

### **Security**
- [ ] HTTPS enabled
- [ ] CORS configured
- [ ] Rate limiting active
- [ ] File validation strict
- [ ] API key validation

### **Performance**
- [ ] Gzip compression enabled
- [ ] Static file caching
- [ ] CDN configured (optional)
- [ ] Database optimized (if used)
- [ ] Memory limits set

### **Monitoring**
- [ ] Health check endpoint
- [ ] Error tracking
- [ ] Performance monitoring
- [ ] Cost tracking
- [ ] Usage analytics

### **Documentation**
- [ ] API documentation
- [ ] User guide
- [ ] Troubleshooting guide
- [ ] Deployment guide
- [ ] Maintenance procedures

---

## 🎯 Success Metrics

### **Technical KPIs**
- Upload success rate: >95%
- Processing success rate: >90%
- Average processing time: <60 seconds
- Error rate: <5%
- Uptime: >99%

### **User Experience KPIs**
- Time to first result: <2 minutes
- User satisfaction: >4.5/5
- Task completion rate: >85%
- Support ticket rate: <2%

### **Business KPIs**
- Cost per processed image: <$0.05
- User retention: >60%
- Processing volume growth: >20% monthly

---

## 📚 Additional Resources

### **Runware Documentation**
- [API Reference](https://runware.ai/docs)
- [Model Guide](https://runware.ai/docs/models)
- [Pricing](https://runware.ai/pricing)

### **Best Practices**
- Always validate inputs client-side AND server-side
- Implement proper error boundaries
- Use progressive enhancement
- Cache responses when possible
- Monitor API usage and costs
- Provide clear user feedback
- Handle edge cases gracefully

### **Common Pitfalls to Avoid**
- ❌ Not validating file types properly
- ❌ Missing error handling for API failures
- ❌ Not implementing rate limiting
- ❌ Storing API keys in frontend code
- ❌ Not cleaning up temporary files
- ❌ Missing progress feedback for users
- ❌ Not handling network timeouts

---

**This scratchpad contains all the essential information needed to build a complete, production-ready Runware product photography application. Follow the guidelines, use the code examples, and test against the criteria for best results.**